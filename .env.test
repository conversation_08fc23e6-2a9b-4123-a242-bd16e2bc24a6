NODE_ENV = production

BABEL_ENV = production
#在非production配置下必须填写，否则路由懒加载会失效！！！
#在非production配置下必须填写，否则路由懒加载会失效！！！
#在非production配置下必须填写，否则路由懒加载会失效！！！

# 页面标题
VUE_APP_TITLE = Incap PO Test

#跨页全选的数量限制
VUE_APP_SELECT_ALL_COUNT = 1000

#附件大小限制 MB
VUE_APP_FILESIZE = 100

# 测试环境配置
ENV = 'staging'

# endpoint of API
VUE_APP_BASE_API = 'http://incap_test.esicint.com'

# 多租户的开关
VUE_APP_TENANT_ENABLE = true

# 文档的开关
VUE_APP_DOC_ENABLE = false

# 百度统计
VUE_APP_BAIDU_CODE = fadc1bd5db1a1d6f581df60a1807f8ab

# 水印
VUE_APP_WATER_MARK=''

# 第三方登录开关
VUE_APP_SOCIAL_LOGIN= true
