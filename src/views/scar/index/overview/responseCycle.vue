
<template>
  <div>
    <div style="display: flex;justify-content: center;align-items: center;margin-bottom: 25px;">
      <el-select
        v-model="queryParams.supplierIds"
        class="content"
        :placeholder="$t('供应商')"
        filterable
        remote
        style="flex: 0 1 40%"
        multiple
        :remote-method="doGetSupplierList"
      >
        <el-option
          v-for="dict in supplierList"
          :key="dict.id"
          :label="dict.name"
          :value="dict.id"
        />
      </el-select>

      <el-select
        style="width: 130px"
        v-model="queryParams.year"
        :placeholder="$t('年份')"
      >
        <el-option label="2025" value="2025" />
        <el-option label="2024" value="2024" />
        <el-option label="2023" value="2023" />
      </el-select>
      <el-button plain type="primary" @click="queryParams.pageNo = 1;doSearch();">{{ $t('common.search') }}</el-button>
      <el-button style="margin-left: 0" @click="resetQuery">{{ $t('common.reset') }}</el-button>
    </div>
    <div style="margin: 5px 0">
      <el-button type="primary" icon="el-icon-download" @click="downloadReport">下载报告</el-button>
      <el-button v-if="showMode === 'list'" plain @click="showMode = 'chart';getChartData()">查看图表</el-button>
      <el-button v-if="showMode === 'chart'" type="primary"  @click="showMode = 'list'">查看表格</el-button>

    </div>
    <div v-if="showMode === 'list'">
      <!-- 汇总报告表格 -->
      <el-table
        :data="list"
        border
        stripe
        v-loading="loading"
      >

      </el-table>

      <!-- 分页 -->
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNo"
        :limit.sync="queryParams.pageSize"
        @pagination="doSearch"
      />
    </div>
    <div v-if="showMode === 'chart'">
      <!-- 柱状图 -->
      <div ref="chartContainer" style="width: 100%; height: 600px;"></div>
    </div>


  </div>
</template>
<script >
import {
  exportReplyCycleExcel,
   getReplyCycleChart,
  getReplyCyclePage,
} from '@/api/scar/report'
import * as echarts from 'echarts'
import { getSupplierDetail } from '@/api/scar'

export default {
  name: 'responseCycle',
  data() {
    return {
      loading: false,
      list: [],
      total: 0,
      queryParams: {
        supplierIds: [],
        year: '2025',
        pageNo: 1,
        pageSize: 10
      },
      showMode: 'list',
      chartInstance: null,
      chartData: {
        data: [],
        series: []
      },
      supplierList: []
    }
  },
  mounted() {
    this.initData()
  },
  beforeDestroy() {
    // 销毁图表实例
    if (this.chartInstance) {
      this.chartInstance.dispose()
      this.chartInstance = null
    }
    // 移除窗口大小变化监听
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {

    initData() {
      this.doSearch()
    },
    doSearch() {
      this.loading = true
      getReplyCyclePage(this.queryParams).then(res => {
        if (res.code === 0) {
          this.list = res.data.list || []
          this.total = res.data.total || 0
        } else {
          this.$message.error(res.msg || '查询失败')
        }
      }).catch(error => {
        console.error('查询失败:', error)
        this.$message.error('查询失败，请稍后重试')
      }).finally(() => {
        this.loading = false
      })
    },
    resetQuery() {
      this.queryParams = {
        supplierIds: [],
        year: '2025',
        pageNo: 1,
        pageSize: 10
      }
      this.doSearch()
    },
    getChartData(){
      getReplyCycleChart(this.queryParams).then(res => {
        if (res.code === 0) {
          this.chartData = res.data
          this.$nextTick(() => {
            this.initChart()
          })
        } else {
          this.$message.error(res.msg || '获取图表数据失败')
        }
      }).catch(error => {
        console.error('获取图表数据失败:', error)
        this.$message.error('获取图表数据失败，请稍后重试')
      })
    },
    initChart() {
      if (!this.$refs.chartContainer) return

      // 销毁已存在的图表实例
      if (this.chartInstance) {
        this.chartInstance.dispose()
      }

      // 创建新的图表实例
      this.chartInstance = echarts.init(this.$refs.chartContainer)

      // 配置图表选项
      const option = {
        // title: {
        //   left: 'center',
        //   textStyle: {
        //     fontSize: 16,
        //     fontWeight: 'bold'
        //   }
        // },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          data: this.chartData.series.map(item => item.name),
          top: '10%'
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          top: '20%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: this.chartData.data,
          axisLabel: {
            rotate: 45,
            interval: 0
          }
        },
        yAxis: {
          type: 'value'
        },
        series: this.chartData.series.map(item => ({
          name: item.name,
          type: item.type,
          stack: item.stack,
          data: item.data,
          itemStyle: {
            borderRadius: [2, 2, 0, 0]
          }
        }))
      }

      // 设置图表配置
      this.chartInstance.setOption(option)

      // 监听窗口大小变化
      window.addEventListener('resize', this.handleResize)
    },
    handleResize() {
      if (this.chartInstance) {
        this.chartInstance.resize()
      }
    },
    downloadReport() {
      exportReplyCycleExcel(this.queryParams).then(res => {
        this.$download.excel(res, '汇总报告.xlsx')
      })
    },
    doGetSupplierList(query) {
      if (query) {
        getSupplierDetail({
          fuzzySupplierName: query
        }).then(res => {
          this.supplierList = res.data
        })
      } else {
        this.supplierList = []
      }
    },
  }
}
</script>

<style scoped lang="scss">

</style>
