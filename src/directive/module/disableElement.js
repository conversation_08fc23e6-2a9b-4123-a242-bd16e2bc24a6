
export default {
  // When all the children of the parent component have been updated
  componentUpdated: function(el, binding) {
    // if (!binding.value) return
    const tags = ['input', 'textarea', 'select']
    tags.forEach(tagName => {
      const nodes = el.getElementsByTagName(tagName)
      for (let i = 0; i < nodes.length; i++) {
        nodes[i].disabled = binding.value
        nodes[i].tabIndex = -1
      }
    })
  }
}

