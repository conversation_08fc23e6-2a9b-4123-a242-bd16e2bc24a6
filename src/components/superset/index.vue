<template>
  <div :ref="refName" style="height: 500px;width: 100%" />
</template>

<script>
import { embedDashboard } from '@superset-ui/embedded-sdk'
import { guestToken } from '@/api/system/superset/superset'

export default {
  name: 'Superset',
  props: {
    dashboardKey: {
      type: String,
      default: ''
    },
    dashboardType: {
      type: String,
      default: 'dashboard'
    },
    dashboardStyle: {
      type: Object,
      default: () => ({
        width: '',
        height: '',
        border: ''
      })
    }
  },
  data() {
    return {
      refName: Math.random().toString(36).substr(2, 9)
    }
  },
  mounted() {
    this.initSuperset()
  },
  methods: {
    initSuperset() {
      embedDashboard({
        id: this.dashboardKey, // given by the Superset embedding UI
        supersetDomain: process.env.VUE_APP_SUPERSET_URL, // 这里结尾不可以带/，不然图表不显示也不报错，TM的花了我好多的时间才发现，zqc
        mountPoint: this.$refs[this.refName], // any html element that can contain an iframe
        fetchGuestToken: async() => {
          const data = await guestToken({
            key: this.dashboardKey,
            type: this.dashboardType
          })
          return data.data
        },
        dashboardUiConfig: { // dashboard UI config: hideTitle, hideTab, hideChartControls, filters.visible, filters.expanded (optional)
          hideTitle: true,
          hideChartControls: false,
          hideTab: true
        }
      })
      const iframe = this.$refs[this.refName].querySelector('iframe')
      if (iframe) {
        iframe.style.width = this.dashboardStyle.width // 设置 iframe 宽度
        iframe.style.height = this.dashboardStyle.height // 设置 iframe 高度
        iframe.style.border = this.dashboardStyle.border
      }
    }
  }
}
</script>

<style scoped>
</style>
