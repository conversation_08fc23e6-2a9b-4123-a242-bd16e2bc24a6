<template>
  <div>
    <div style="display: flex;">
      <el-skeleton v-if="loading" animated style="flex-basis: 100%" />

      <div v-else style="flex-basis: 90%">
        <el-timeline>
          <el-timeline-item
            v-for="(item, index) in recordList"
            :key="index"
            :color="'#456291'"
          >
            <el-row :gutter="24">
              <el-col :span="5">{{ item.time }}</el-col>
              <el-col :span="4">{{ item.userName }}</el-col>
              <el-col :span="10">
                <div style="vertical-align: top;white-space: pre-wrap;">{{ item.content }}</div>
              </el-col>
            </el-row>
          </el-timeline-item>
        </el-timeline>
      </div>
    </div>
    <div style="text-align: center">
      <el-button type="primary" @click="$emit('update:logVisible',false)">关闭</el-button>
    </div>
  </div>

</template>

<script>
import { getDataOperateRecord } from '@/api/system/dataOperateRecord'
import { parseTime } from '@/utils/ruoyi'

export default {
  name: 'OperationRecord',
  props: ['businessId', 'columns'],
  data() {
    return {
      recordList: [{
        content: '',
        time: ''
      }],
      loading: false

    }
  },
  mounted() {
    this.getLog()
  },
  methods: {
    getLog() {
      this.loading = true
      getDataOperateRecord({
        businessId: this.businessId,
        columns: this.columns
      }).then(res => {
        this.loading = false
        this.recordList = res.data
        this.recordList?.forEach(item => {
          item.content = `${item.logContent?.replaceAll('<br/>', '')}`
          // item.time = `${parseTime(item.createTime)}\n${item.userName}`
          item.time = `${parseTime(item.createTime)}`
        })
      })
    }
  }
}
</script>

<style scoped>

</style>
