<template>
  <div v-if="customForm.sections&&customForm.sections.length>0" v-disable-all="viewOnly">
    <el-card v-for="section in customForm.sections" :key="section.id">
      <div slot="header">
        <span style="font-weight: bold;color:#4996b8 " class="sectionTab">
          {{ section.name }}
        </span>
      </div>
      <el-form
        v-for="attr in section.schemas"
        :key="attr.id"
        ref="customForm"
        :model="attr"
        :style="{width: `${100/section.numOfElement}%`}"
        inline
        label-position="top"
        size="mini"
        style="display: inline-block;"
      >
        <el-form-item
          :label="attr.name"
          :rules="attr.isMandatory?customNoRules:customRules"
          class="commonForm"
          prop="value"
        >
          <show-or-edit
            v-if="attr.attrType === 'textarea'"
            :disabled="viewOnly"
            :value="attr.value"
          >
            <el-input v-if="attr.attrType === 'textarea'" v-model="attr.value" class="commonForm" />
          </show-or-edit>
          <show-or-edit
            v-else-if="attr.attrType === 'date'"
            :disabled="viewOnly"
            type="Date"
            :value="attr.value"
          >
            <el-date-picker
              v-if="attr.attrType === 'date'"
              v-model="attr.value"
              :disabled="viewOnly"
              class="commonForm"
              :placeholder="$t('common.pleaseSelectADate')"
              type="date"
              placement="bottom-start"
              value-format="yyyy-MM-dd"
            />
          </show-or-edit>
          <show-or-edit
            v-else-if="attr.attrType === 'datetime'"
            :disabled="viewOnly"
            type="DateTime"
            :value="attr.value"
          >
            <el-date-picker
              v-if="attr.attrType === 'datetime'"
              v-model="attr.value"
              :disabled="viewOnly"
              class="commonForm"
              :placeholder="$t('common.pleaseSelectADate')"
              type="datetime"
              placement="bottom-start"
              value-format="yyyy-MM-dd h:m"
            />
          </show-or-edit>
          <show-or-edit
            v-else-if="attr.attrType === 'checkbox'||attr.attrType === 'radio'"
            :disabled="viewOnly"
            :value="attr.value"
            :dict="attr.dictType"
          >
            <el-select
              v-if="attr.attrType === 'checkbox'||attr.attrType === 'radio'"
              v-model="attr.value"
              :disabled="viewOnly"
              :multiple="attr.attrType === 'checkbox'"
              class="commonForm"
            >
              <el-option v-for="opt in getDictDatas(attr.dictType)" :key="opt.id" :label="opt.label" :value="opt.value" />
            </el-select>
          </show-or-edit>
        </el-form-item>
      </el-form>

    </el-card>
  </div>
</template>

<script>
import { getCustomFormListDetail, getCustomFormListValues } from '@/api/system/customForm'
import ShowOrEdit from '@/components/ShowOrEdit'
export default {
  name: 'CustomForm',
  components: {
    ShowOrEdit
  },
  props: {
    code: {
      type: String,
      required: true
    },
    viewOnly: {
      type: Boolean,
      required: true,
      default: false
    }
  },
  data() {
    return {
      customForm: {},
      formId: '',
      customRules: { required: true, message: '请输入', trigger: 'blur' },
      customNoRules: { required: false, message: '请输入', trigger: 'blur' }
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      getCustomFormListDetail({ code: this.code }).then((res) => {
        if (res.data) {
          res.data.sections?.forEach(item => {
            item.schemas?.forEach(sche => {
              if (item.attrType === 'checkbox') {
                this.$set(sche, 'value', [])
              } else {
                this.$set(sche, 'value', '')
              }
            })
          })
          this.customForm = res.data
          this.formId = res.data.id
        }
      })
    },
    getCustomFormValue(businessId) {
      if (this.formId) {
        getCustomFormListValues({
          formId: this.formId,
          businessId
        }).then(res => {
          this.customForm.sections?.forEach(item => {
            item.schemas?.forEach(sche => {
              try {
                sche.value = JSON.parse(res.data.find(item => item.schemasId === sche.id)?.value)
              } catch (e) {
                console.log(e)
              }
            })
          })
        })
      }
    },
    submitCustom() {
      let pass = true
      const data = []
      this.$refs.customForm?.map(item => item.validate(valid => {
        if (!valid) {
          pass = false
        }
      }))
      if (pass) {
        this.customForm.sections?.forEach(item => {
          item.schemas?.forEach(sche => {
            data.push({
              formId: this.customForm.id,
              schemasId: sche.id,
              sectionId: item.id,
              value: JSON.stringify(sche.value)
            })
          })
        })
      }
      return pass ? data : ''
    }
  }
}
</script>

<style lang="scss" scoped>
.commonForm {
  width: 85%;
  margin-bottom: 14px;
}
::v-deep .el-card__header{
  border-top: 3px solid #4996b8;
  border-bottom: none;
}
.sectionTab{
  font-size: 16px;
  font-weight: 700;
}
</style>
