<template>
  <div>
    <div
      v-if="defaultShowHead"
      style="text-align: right;color: #689CB7;font-size: 14px;cursor:pointer;"
      @click="showHead =!showHead"
    >
      {{ $t('common.statisticians') }}
      <i class="el-icon-s-data" style="font-size: 20px;" />
    </div>
    <Transition name="scale">
      <div v-show="showHead" class="flexContainer" :style="`justify-content: ${contentStyle}`">
        <div v-for="item in itemArr">

          <div class="realTab">
            <div style="display: flex;justify-content: space-between">
              <span>{{ item.label }}</span>
            </div>
            <div class="realNum">
              <number-format
                :decimal-place="item.decimalPlace?item.decimalPlace:store.getters.decimalPlace"
                :value="item.value"
              />
              <span>{{ item.unit }}</span>
            </div>
          </div>
          <div style="text-align: right;margin-top: 5px;">

            <el-button type="text" @click="pushRoute(item)">{{ item.linkName }}</el-button>
          </div>
        </div>

      </div>
    </Transition>
  </div>
</template>

<script>
import store from '../../store'

export default {
  name: 'StatisticsCard',
  props: {
    itemArr: {
      type: Array,
      default() {
        return [{ label: '', value: '' }]
      }
    },
    defaultShow: {
      type: Boolean,
      default: false
    },
    defaultShowHead: {
      type: Boolean,
      default: true
    },
    contentStyle: {
      type: String
    }
  },
  data() {
    return {
      showHead: false
    }
  },
  computed: {
    store() {
      return store
    }
  },
  mounted() {
    this.showHead = this.defaultShow
  },
  methods: {
    pushRoute(item) {
      if (item.action){
        //带参数的跳转
        this.$store.commit('setParameters', item.action)
      }
      this.$router.push(item.link)
    }
  }
}
</script>

<style scoped>
.flexContainer {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  min-width: 1000px;
  margin: 20px 0
}

.realTab {
  width: 220px;
  height: 90px;
  font-size: 16px;
  background: rgb(242, 244, 249);
  color: #000000;
  display: flex;
  justify-content: center;
  flex-direction: column;
  padding: 21px;
  margin-left: 30px;
}

.realNum {
  color: #327da1;
  font-size: 25px;
}

.scale-leave-active {
  -webkit-animation: scale-down-tr 0.4s cubic-bezier(0.250, 0.460, 0.450, 0.940) both;
  animation: scale-down-tr 0.4s cubic-bezier(0.250, 0.460, 0.450, 0.940) both;
}

.scale-enter-active {
  -webkit-animation: scale-up-tr 0.4s cubic-bezier(0.390, 0.575, 0.565, 1.000) both;
  animation: scale-up-tr 0.4s cubic-bezier(0.390, 0.575, 0.565, 1.000) both;
}

@-webkit-keyframes scale-up-tr {
  0% {
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
    -webkit-transform-origin: 100% 0%;
    transform-origin: 100% 0%;
  }
  100% {
    -webkit-transform: scale(1);
    transform: scale(1);
    -webkit-transform-origin: 100% 0%;
    transform-origin: 100% 0%;
  }
}

@keyframes scale-up-tr {
  0% {
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
    -webkit-transform-origin: 100% 0%;
    transform-origin: 100% 0%;
  }
  100% {
    -webkit-transform: scale(1);
    transform: scale(1);
    -webkit-transform-origin: 100% 0%;
    transform-origin: 100% 0%;
  }
}

@-webkit-keyframes scale-down-tr {
  0% {
    -webkit-transform: scale(1);
    transform: scale(1);
    -webkit-transform-origin: 100% 0%;
    transform-origin: 100% 0%;
  }
  100% {
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
    -webkit-transform-origin: 100% 0%;
    transform-origin: 100% 0%;
  }
}

@keyframes scale-down-tr {
  0% {
    -webkit-transform: scale(1);
    transform: scale(1);
    -webkit-transform-origin: 100% 0%;
    transform-origin: 100% 0%;
  }
  100% {
    -webkit-transform: scale(0);
    transform: scale(0);
    -webkit-transform-origin: 100% 0%;
    transform-origin: 100% 0%;
  }
}
</style>
