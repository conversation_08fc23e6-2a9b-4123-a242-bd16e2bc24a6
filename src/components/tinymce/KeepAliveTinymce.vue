<template>
  <div class="keep-alive-tinymce">
    <tinymce 
      ref="tinymce"
      v-model="internalValue" 
      v-bind="$attrs"
      v-on="$listeners"
    />
  </div>
</template>

<script>
import Tinymce from './index.vue'

export default {
  name: 'KeepAliveTinymce',
  components: {
    Tinymce
  },
  props: {
    value: {
      default: ''
    }
  },
  data() {
    return {
      internalValue: this.value,
      // 用于存储keep-alive状态下的内容
      keepAliveContent: '',
      isKeepAliveActive: true
    }
  },
  watch: {
    value: {
      handler(newVal) {
        if (newVal !== this.internalValue) {
          this.internalValue = newVal
        }
      },
      immediate: true
    },
    internalValue(newVal) {
      this.keepAliveContent = newVal
      this.$emit('input', newVal)
    }
  },
  activated() {
    // keep-alive组件激活时
    this.isKeepAliveActive = true
    this.$nextTick(() => {
      // 恢复内容
      if (this.keepAliveContent && this.keepAliveContent !== this.internalValue) {
        this.internalValue = this.keepAliveContent
      }
    })
  },
  deactivated() {
    // keep-alive组件停用时
    this.isKeepAliveActive = false
    // 保存当前内容
    this.keepAliveContent = this.internalValue
  },
  beforeDestroy() {
    // 组件销毁前清理
    this.keepAliveContent = ''
  },
  methods: {
    // 代理方法，供外部调用
    getContentNum() {
      return this.$refs.tinymce?.getContentNum?.()
    },
    // 手动保存内容
    saveContent() {
      this.keepAliveContent = this.internalValue
    },
    // 手动恢复内容
    restoreContent() {
      if (this.keepAliveContent) {
        this.internalValue = this.keepAliveContent
      }
    }
  }
}
</script>

<style scoped>
.keep-alive-tinymce {
  width: 100%;
}
</style>
