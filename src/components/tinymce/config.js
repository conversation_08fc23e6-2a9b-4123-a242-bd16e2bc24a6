/* eslint-disable max-len */

export const plugins = [
  // 'advlist anchor autolink autosave code codesample directionality emoticons fullscreen hr image imagetools insertdatetime link lists media nonbreaking noneditable pagebreak paste preview print save searchreplace spellchecker tabfocus table template textpattern visualblocks visualchars wordcount'
  'lists wordcount table image link'
]
export const toolbar = [
  // 'code searchreplace bold italic underline strikethrough alignleft aligncenter alignright outdent indent blockquote removeformat subscript superscript codesample hr bullist numlist link image charmap preview anchor pagebreak insertdatetime media table emoticons forecolor backcolor fullscreen wordcount'
  'formatselect bold italic strikethrough forecolor bullist numlist link image charmap preview anchor pagebreak insertdatetime media table fontsizeselect fontselect'
]
