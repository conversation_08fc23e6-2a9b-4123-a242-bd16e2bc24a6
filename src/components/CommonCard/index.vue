<template>
  <el-card class="commonCard">
    <div slot="header" class="mainTab">
      {{ title }}
      <slot name="header" />
      <span
        class="hotBig"
        style="font-size: 18px;cursor:pointer;float: right;transition: transform .3s;position:relative;"
        @click="switchgear"
      >
        <i
          class="el-icon-arrow-up"
          :style="showDetail? '':{transform: 'rotate(180deg)'}"
          style="font-weight: bold;"
        />
      </span>

    </div>
    <div v-show="showDetail" v-if="refreshFlag">
      <slot />
    </div>
  </el-card>

</template>

<script>
export default {
  name: 'CommonCard',
  props: ['title', 'defaultClose', 'isRefresh'],
  data() {
    return {
      showDetail: true,
      refreshFlag: true
    }
  },
  mounted() {
    // 此处的判断是commonCard组件需要时间渲染，当用于工作流的流程图处时，可能使用的渲染时间超出原先设置100ms，流程图还没渲染成功就被打断渲染导致流程图出不来。
    if (this.defaultClose) {
      // setTimeout(() => {
      this.$nextTick(() => {
        this.showDetail = false
      })
      // }, 500)
    }
  },
  methods: {
    switchgear() {
      this.showDetail = !this.showDetail
      if (this.isRefresh && this.showDetail) {
        this.refreshFlag = false
        setTimeout(() => {
          this.refreshFlag = true
        }, 0)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/assets/styles/card.scss";

.commonCard{
  margin: 10px 0 ;
  .hotBig::before{
    content: '';
    position: absolute;
    top: -10px; right: -10px;
    bottom: -10px; left: -10px;
  }
}
</style>
