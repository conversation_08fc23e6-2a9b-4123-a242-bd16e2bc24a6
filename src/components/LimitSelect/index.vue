<template>
  <!--  该组件用于展示下拉选择框限制数量的封装-->
  <div style="position:relative;">
    <el-select
      v-bind="$attrs"
      v-model="selectedValue"
      style="width: 100%"
      :multiple-limit="multipleLimit"
    >
      <slot />
    </el-select>
    <span
      :style="{
        color: value.length === multipleLimit ? 'red':'#C3C3C3 ' ,
        'margin-left': '10px',
        position:'absolute',
        right: '-40px'}"
    >
      {{ `${value.length}/${multipleLimit}` }}
    </span>
  </div>

</template>

<script>
export default {
  name: 'LimitSelect',
  model: {
    prop: 'value',
    event: 'update:value'
  },
  props: {
    value: {
      type: [Array],
      default: () => []
    },
    multipleLimit: {
      type: Number,
      default: 0
    }
  },

  data() {
    return {
      selectedValue: this.value

    }
  },

  watch: {
    value(newValue) {
      // 这里可以监听 v-model 的变化
      this.$emit('update:value', newValue)
      // console.log('Selected value:', newValue)
    },
    selectedValue(newVal) {
      if (newVal !== this.value) {
        console.log(11)
        this.$emit('update:value', newVal)
      }
    }
  },
  mounted() {
    console.log(this.$attrs)
  }
}
</script>

<style scoped lang="scss">

</style>
