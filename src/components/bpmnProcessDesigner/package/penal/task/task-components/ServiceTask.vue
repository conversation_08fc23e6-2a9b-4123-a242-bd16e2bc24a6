<template>
  <div style="margin-top: 16px">
    <el-form-item label="代理类类名">
      <div>
        <el-input v-model="delegateClass" clearable @change="updateServiceTaskClass" />
      </div>
    </el-form-item>
  </div>
</template>

<script>
export default {
  name: 'ServiceTask',
  data() {
    return {
      delegateClass: ''
    }
  },
  watch: {
    id: {
      immediate: true,
      handler() {
        this.bpmnElement = window.bpmnInstances.bpmnElement
        this.delegateClass = this.bpmnElement.businessObject.class
      }
    }
  },
  methods: {
    updateServiceTaskClass() {
      this.bpmnElement.businessObject.class = this.delegateClass
      // [required] 需要回写时间定义到实际的model对象中，当前的bpmnElement为局部对象
      window.bpmnInstances.modeling.updateProperties(this.bpmnElement, {
        businessObject: this.bpmnElement.businessObject
      })
    }
  }
}
</script>

<style scoped>

</style>
