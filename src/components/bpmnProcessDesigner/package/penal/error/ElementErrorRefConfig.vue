<template>
  <div class="panel-tab__content">
    <div class="element-property input-property">
      <div class="element-property__label">选择错误 </div>
      <div class="element-property__value">
        <el-select
          v-model="errorRef"
          clearable
          filterable
        >
          <el-option
            v-for="item in errorList"
            :key="item.id"
            :label="item.name"
            :value="item"
          />
        </el-select>
      </div>
    </div>
    <el-button class="element-drawer__button" type="primary" @click="updateErrorRef">确定设置</el-button>
  </div>
</template>

<script>
export default {
  name: 'ElementErrorRefConfig',
  props: {
    id: String
  },
  data() {
    return {
      errorList: [],
      errorRef: {}
    }
  },
  watch: {
    id: {
      immediate: true,
      handler() {
        this.bpmnElement = window.bpmnInstances.bpmnElement
        // 找出已有的错误定义
        this.rootElements = window.bpmnInstances.modeler.getDefinitions().rootElements
        this.rootElements.forEach(el => {
          if (el.$type === 'bpmn:Error') {
            this.errorList.push({ ...el })
          }
        })
        this.bpmnElement.businessObject.eventDefinitions?.forEach(modelEl => {
          this.errorRef = { id: modelEl.errorRef.id, name: modelEl.errorRef.name, errorCode: modelEl.errorRef.errorCode }
        })
      }
    }
  },
  methods: {
    // 绑定错误边界事件的错误引用
    updateErrorRef() {
      const errorEventDefinitions = this.bpmnElement.eventDefinitions
      const newErrorRef = window.bpmnInstances.moddle.create('bpmn:Error', {
        $type: 'bpmn:Error',
        errorCode: this.errorRef.errorCode,
        id: this.errorRef.id,
        name: this.errorRef.name
      })
      if (!errorEventDefinitions) {
        const newErrorEventDefinition = window.bpmnInstances.bpmnFactory.create('bpmn:ErrorEventDefinition', {
          $type: 'bpmn:ErrorEventDefinition',
          errorRef: newErrorRef
        })
        this.bpmnElement.eventDefinitions = [newErrorEventDefinition]
      } else {
        // 修改已经存在的定时器触发时间设置
        this.bpmnElement.eventDefinitions?.forEach(def => {
          if (def.$type === 'bpmn:ErrorEventDefinition') {
            def.errorRef = newErrorRef
          }
        })
      }

      // [required] 需要回写时间定义到实际的model对象中，当前的bpmnElement为局部对象
      window.bpmnInstances.modeling.updateProperties(this.bpmnElement, {
        eventDefinitions: this.bpmnElement.eventDefinitions
      })
    }
  }
}
</script>

<style scoped>

</style>
