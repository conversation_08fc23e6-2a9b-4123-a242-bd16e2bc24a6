<template>
  <div class="panel-tab__content">
    <el-form size="medium">
      <el-form-item label="填写时间（ISO-8601格式或者时间变量${StartDateTime}">
        <el-input
          v-model="timeDate"
          :placeholder="$t('选择日期')"
        />
      </el-form-item>
    </el-form>
    <el-button class="element-drawer__button" type="primary" @click="updateElementTimer">确定设置</el-button>
  </div>
</template>

<script>
export default {
  name: 'ElementTimerConfig',
  inject: {
    prefix: 'prefix'
  },
  props: {
    id: String
  },
  data() {
    return {
      timeDate: '' // 定时器的开始时间
    }
  },
  watch: {
    id: {
      immediate: true,
      handler() {
        this.bpmnElement = window.bpmnInstances.bpmnElement
        //  回写已经设置的定时器时间
        // 每个时间都是一个moddleElement对象。参考console.log的结果即可。
        this.bpmnElement.businessObject.eventDefinitions?.forEach(modelEl => {
          this.timeDate = modelEl.timeDate.body
        })
      }
    }
  },
  methods: {
    //    标准的定时器触发时间设置定义
    // <intermediateCatchEvent id="sid-485EE2C5-9D8B-4587-9F99-B036243D9FF6">x
    // <timerEventDefinition>
    //   <timeDate>2033-07-31T10:00:00Z</timeDate>
    // </timerEventDefinition>
    // </intermediateCatchEvent>

    // 手动修改TimerIntermediateCatchEvent的Timer event definition的属性值[timeDate]
    updateElementTimer() {
      const newTimeDate = window.bpmnInstances.moddle.create('bpmn:Expression', {
        $type: 'bpmn:Expression',
        body: this.timeDate
      })
      const timerEventDefinitions = this.bpmnElement.businessObject.eventDefinitions

      if (!timerEventDefinitions) {
        const newTimerEventDefinition = window.bpmnInstances.moddle.create('bpmn:TimerEventDefinition', {
          $type: 'bpmn:TimerEventDefinition',
          timeDate: newTimeDate
        })
        // 将新的 Timer Event Definition 添加到 Timer Intermediate Catch Event 中
        this.bpmnElement.businessObject.eventDefinitions = [newTimerEventDefinition]
      } else {
        // 修改已经存在的定时器触发时间设置
        this.bpmnElement.businessObject.eventDefinitions?.forEach(def => {
          if (def.$type === 'bpmn:TimerEventDefinition') {
            def.timeDate = newTimeDate
          }
        })
      }
      // [required] 需要回写时间定义到实际的model对象中，当前的bpmnElement为局部对象
      window.bpmnInstances.modeling.updateProperties(this.bpmnElement, {
        eventDefinitions: this.bpmnElement.businessObject.eventDefinitions
      })
    }
  }
}
</script>

<style scoped>

</style>
