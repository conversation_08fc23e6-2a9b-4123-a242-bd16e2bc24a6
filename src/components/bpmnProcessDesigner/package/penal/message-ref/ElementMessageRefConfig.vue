<template>
  <div class="panel-tab__content">
    <div class="element-property input-property">
      <div class="element-property__label">选择消息 </div>
      <div class="element-property__value">
        <el-select
          v-model="messageRef"
          clearable
          filterable
        >
          <el-option
            v-for="item in msgList"
            :key="item.id"
            :label="item.name"
            :value="item"
          />
        </el-select>
      </div>
    </div>
    <el-button class="element-drawer__button" type="primary" @click="updateMessageRef">确定设置</el-button>
  </div>
</template>

<script>
export default {
  name: 'ElementMessageRefConfig',
  props: {
    id: String
  },
  data() {
    return {
      msgList: [],
      messageRef: {}
    }
  },
  watch: {
    id: {
      immediate: true,
      handler() {
        this.bpmnElement = window.bpmnInstances.bpmnElement
        // 找出已有的错误定义
        this.rootElements = window.bpmnInstances.modeler.getDefinitions().rootElements
        this.rootElements.forEach(el => {
          if (el.$type === 'bpmn:Message') {
            this.msgList.push({ ...el })
          }
        })
        this.bpmnElement.businessObject.eventDefinitions?.forEach(modelEl => {
          this.messageRef = { id: modelEl.messageRef.id, name: modelEl.messageRef.name }
        })
      }
    }
  },
  methods: {
    // 绑定错误边界事件的错误引用
    // 需要顶一个Message对应的 ModdleElement对象出来才可以赋值给eventDefinition
    updateMessageRef() {
      const messageEventDefinitions = this.bpmnElement.eventDefinitions
      const newMessageRef = window.bpmnInstances.moddle.create('bpmn:Message', {
        $type: 'bpmn:Message',
        id: this.messageRef.id,
        name: this.messageRef.name
      })
      if (!messageEventDefinitions) {
        const newMessageEventDefinitions = window.bpmnInstances.bpmnFactory.create('bpmn:MessageEventDefinition', {
          $type: 'bpmn:MessageEventDefinition',
          messageRef: newMessageRef
        })
        this.bpmnElement.eventDefinitions = [newMessageEventDefinitions]
      } else {
        // 修改已经存在的定时器触发时间设置
        this.bpmnElement.eventDefinitions?.forEach(def => {
          if (def.$type === 'bpmn:MessageEventDefinition') {
            def.messageRef = newMessageRef
          }
        })
      }
      // [required] 需要回写时间定义到实际的model对象中，当前的bpmnElement为局部对象
      window.bpmnInstances.modeling.updateProperties(this.bpmnElement, {
        eventDefinitions: this.bpmnElement.eventDefinitions
      })
    }
  }
}
</script>

<style scoped>

</style>
