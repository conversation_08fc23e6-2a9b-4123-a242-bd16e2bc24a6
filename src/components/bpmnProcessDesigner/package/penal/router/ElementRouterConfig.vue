<template>
  <div class="panel-tab__content">
    <div class="element-property input-property">
      <div class="element-property__label">选择路由 </div>
      <div class="element-property__value">
        <el-input v-model="selectedRouter" placeholder="请输入路由路径" clearable />
      </div>
      <el-button type="text" @click="saveAttribute">
        保存
      </el-button>
    </div>
  </div>
</template>
<script>

export default {
  name: 'ElementRouterConfig',
  props: {
    id: String
  },
  inject: {
    prefix: 'prefix',
    width: 'width'
  },
  data() {
    return {
      selectedRouter: '',
      routerKey: -1 // 此element是否存在路由扩展参数？有的话赋值此index。标识参数在扩展参数集合中的位置
    }
  },
  watch: {
    id: {
      immediate: true,
      handler: function(id) {
        this.selectedRouter = ''
        this.routerKey = -1
        this.bpmnElement = window.bpmnInstances.bpmnElement;
        this.otherExtensionList = []; // 其他扩展配置
        this.bpmnElementProperties =
          this.bpmnElement.businessObject?.extensionElements?.values?.filter(ex => {
            if (ex.$type !== `${this.prefix}:Properties`) {
              this.otherExtensionList.push(ex);
            }
            console.log('ex.values', ex.values)
            // 找出该节点配置的路由
            for (let i = 0; i < ex.values?.length; i++) {
              if (ex.values[i].name === 'selectedRouter') {
                this.routerKey = i
                this.selectedRouter = ex.values[i].value
              }
            }
            return ex.$type === `${this.prefix}:Properties`;
          }) ?? [];
        // 保存所有的 扩展属性字段
        this.bpmnElementPropertyList = this.bpmnElementProperties.reduce((pre, current) => pre.concat(current.values), []);
      }
    }
  },
  methods: {
    saveAttribute() {
      const name = 'selectedRouter'
      const value = this.selectedRouter
      if (this.routerKey !== -1) {
        // 编辑
        window.bpmnInstances.modeling.updateModdleProperties(this.bpmnElement, this.bpmnElementPropertyList[this.routerKey], {
          name,
          value
        });
      }else {
        // 新建属性字段
        const newPropertyObject = window.bpmnInstances.moddle.create(`${this.prefix}:Property`, { name, value });
        // 新建一个属性字段的保存列表
        const propertiesObject = window.bpmnInstances.moddle.create(`${this.prefix}:Properties`, {
          values: this.bpmnElementPropertyList.concat([newPropertyObject])
        });
        this.updateRouter(propertiesObject);
      }

    },
    // 目前看下来，扩展属性只能放到extensionElement中
    updateRouter(properties) {
      const extensions = window.bpmnInstances.moddle.create("bpmn:ExtensionElements", {
        values: this.otherExtensionList.concat([properties])
      });
      window.bpmnInstances.modeling.updateProperties(this.bpmnElement, {
        extensionElements: extensions
      });
    }
  }
}
</script>

<style scoped>

</style>
