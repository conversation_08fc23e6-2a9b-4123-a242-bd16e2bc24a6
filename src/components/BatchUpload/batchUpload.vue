<template>
  <div>
    <el-button size="mini" type="primary" @click="upload.open=true"  icon="el-icon-upload2" style="margin-right: 10px">
      {{ btnName }}
    </el-button>
    <el-dialog
      v-if="upload.open"
      :title="upload.title"
      :visible.sync="upload.open"
      width="400px"
      append-to-body
    >
      <div class="upload">
        <p>
          {{ $t('common.uploadDescription') }}：
        </p>
        <p>
          {{ $t('common.1uploadDescriptionTitle') }}
        </p>
        <p>
          {{ $t('common.2uploadDescriptionTitle') }}
        </p>
        <p>
          {{ $t('common.3uploadDescriptionTitle') }}
        </p>
        <p>
          {{ $t('common.4uploadDescriptionTitle') }}
        </p>
      </div>
      <div class="text-center">
        <el-upload
          ref="upload"
          class="small-padding"
          :limit="1"
          accept=".xlsx, .xls"
          :headers="upload.headers"
          :action="uploadUrl"
          :disabled="upload.isUploading"
          :on-error="errorFileUpload"
          :on-progress="handleFileUploadProgress"
          :on-success="handleFileImportSuccess"
          :before-upload="beforeUploadXls"
          :auto-upload="false"
          drag
        >
          <i class="el-icon-upload" />
          <div class="el-upload__text">{{ $t('common.clickOrDragTheFileHereToUpload') }}</div>
        </el-upload>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="upload.open = false">{{ $t('common.cancel') }}</el-button>
        <el-button v-if="templateDownload&&templateName" type="primary" plain @click="importTemplate"> {{ $t('common.downloadTemplate') }}</el-button>
        <el-button type="primary" @click="submitFileForm"> {{ $t('common.confirm') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { getBaseHeader } from '@/utils/request'
import dayjs from 'dayjs'
import $modal from "@/plugins/modal";

export default {
  name: 'BatchUpload',
  props: {
    btnName: {
      type: String,
      default: '批量添加'
    },
    url: {
      type: String,
      required: true
    },
    templateName: {
      type: String,
      required: false
    },
    templateDownload: {
      type: Function,
      required: false
    },
    afterUpload: {
      type: Function,
      required: false
    }

  },
  data() {
    return {
      upload: {
        // 是否显示弹出层（物料导入）
        open: false,
        // 弹出层标题（物料导入）
        title: this.$t('financial.batchAdd'),
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: getBaseHeader()
        // 上传的地址
        // url: process.env.VUE_APP_BASE_API + '/admin-api/financial/invoice-info/import'
      }}
  },
  computed: {
    uploadUrl() {
      return `${process.env.VUE_APP_BASE_API}/admin-api/${this.url}`
    }
  },
  methods: {
    submitFileForm() {
      this.$refs.upload.submit()
    },
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true
    },
    errorFileUpload(event, file, fileList) {
      const isLt2M = file.size / 1024 / 1024 < process.env.VUE_APP_FILESIZE
      if (!isLt2M) {
        this.$message.error(this.$t('common.uploadFileSizeCannotExceedm', { fileSize: process.env.VUE_APP_FILESIZE }))
      }
      this.$refs.upload.clearFiles() // 上传成功之后清除历史记录
      this.upload.isUploading = false
    },
    beforeUploadXls(file) {
      if (file.size > process.env.VUE_APP_FILESIZE * 1024 * 1024) {
        $modal.msgError(this.$t('common.uploadFileSizeCannotExceedm', { fileSize: process.env.VUE_APP_FILESIZE }))
        return false
      }
      if (!['xls', 'xlsx'].includes(file.name.slice(file.name.lastIndexOf('.') + 1))) {
        $modal.msgError(this.$t('supplier.unsupportedFileFormat'))
        return false
      }
    },
    handleFileImportSuccess(response, file, fileList) {
      this.upload.open = false
      this.upload.isUploading = false
      this.$refs.upload.clearFiles()
      if (response.code !== 0) {
        this.$modal.msgError(response.msg)
        return
      }
      // 拼接提示语
      const data = response.data
      let text = ''
      if (data.create && data.create.length > 0) {
        text += this.$t('common.successfullyCreatedQuantity') + '：' + data.create.length
      }
      if (data.update && data.update.length > 0) {
        text += '更新成功数量：' + data.update.length
      }
      if (data.failure && Object.keys(data.failure).length > 0) {
        text += '<br />' + this.$t('common.numberOfCreationFailures') + '：' + Object.keys(data.failure).length
      }
      if (data.filePath) {
        text += '<br />' + this.$t('common.numberOfValidationFailures')
        text += '<br /><a class="el-button--text" target="_blank" href="' + data.filePath + '" style="text-decoration: underline">' + this.$t('common.verificationFailedDataDownload') + '</a>'
      }

      this.$alert(text, this.$t('supplier.importResults'), { dangerouslyUseHTMLString: true })
      if (this.afterUpload) {
        this.afterUpload()
      }
      // response.data.forEach(a => { a.invoiceDate = a.invoiceDate ? dayjs(a.invoiceDate).format('YYYY-MM-DD') : '' })
      // this.invoiceInfo.push(...response.data)
      // this.$message.success('导入成功')
    },
    async importTemplate() {
      const data = await this.templateDownload()
      this.$download.excel(data, `${this.templateName}.xlsx`)
    }
  }
}
</script>
<style scoped lang="scss">

</style>
