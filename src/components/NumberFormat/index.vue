<template>
  <span
    :style="styleColor"
  > {{ formatNum + splicing }}</span>
</template>

<script>
import store from '@/store'
export default {
  name: 'NumberFormat',
  props: {
    decimalPlace: {
      type: Number,
      default: () => store.getters.decimalPlace // 默认2位小数
    },
    removeZero: {
      type: Boolean,
      default: () => true // 默认去除末尾的0
    },
    paddingZero: {
      type: Boolean,
      default: false // 默认不补0
    },
    value: [Number, String],
    splicing: { type: String, default: '' }, // 拼接符号，如需在数字后面拼接*
    fontColor: { type: String, default: '' } // 字体颜色
  },
  computed: {
    formatNum() {
      if (!isNaN(parseFloat(this.value)) && isFinite(this.value)) {
        let returnvalue = (Math.round((Number(this.value) + Number.EPSILON) * Math.pow(10, this.decimalPlace)) /
          (Math.pow(10, this.decimalPlace))).toString()
        if (returnvalue.indexOf('.') > 0) {
          if (this.removeZero) {
            returnvalue = returnvalue.replace(/\.?0+$/, '')
          } else if (this.paddingZero) {
            returnvalue = returnvalue.replace(/(\.\d*?[1-9])0+$/, '$1')
          }
        }
        return returnvalue
      } else if (this.value === 0) {
        return '0' + (this.paddingZero ? '.' + '0'.repeat(this.decimalPlace) : '')
      } else if (this.value === '***') {
        return this.value
      } else {
        return ''
      }
    },
    styleColor() {
      return this.fontColor && this.value !== '***' ? `color: ${this.fontColor}` : ''
    }
  }
}
</script>
