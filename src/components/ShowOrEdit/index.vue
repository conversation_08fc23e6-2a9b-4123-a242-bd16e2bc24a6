<template>
  <!--  查看编辑组件测试-->
  <span style="word-break: break-all">

    <slot v-if="!disabled" />
    <slot v-else-if="type === 'Custom'" name="content" />
    <span v-else-if="type==='Date'">
      {{ value?dayjs(value).format('YYYY-MM-DD'):'' }}
    </span>
    <span v-else-if="type==='DateTime'">
      {{ value?dayjs(value).format('YYYY-MM-DD HH:mm:ss'):'' }}
    </span>
    <span v-else-if="type==='Boolean'">
      {{ value==null?"":value? $t('auth.yes') :$t('auth.no') }}
    </span>
    <span v-else-if="type==='Number'">
      <number-format v-if="decimalPlace" :value="value" :decimal-place="decimalPlace" />
      <number-format v-else :value="value" />
    </span>
    <span v-else-if="customList?.length">
      {{ customValue() }}
    </span>
    <span v-else>
      <dict-tag v-if="dict" :value="value" :type="dict" />
      <span v-else>
        {{ value }}
      </span>
    </span>

  </span>

</template>

<script>
import dayjs from 'dayjs'
export default {
  name: 'ShowOrEdit',
  props: ['value', 'dict', 'disabled', 'type', 'customList', 'decimalPlace'],
  data() {
    return {
      dayjs
    }
  },
  methods: {
    customValue() {
      const a = this.customList.find(a => a.id === this.value || a.value === this.value)
      return a?.label || a?.name
    }
  }
}
</script>

<style scoped>

</style>
