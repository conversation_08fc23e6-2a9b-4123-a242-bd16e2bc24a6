<template>
  <span>
    <template v-for="(dict, index) in getDictDatas2(type, value)">
      <!-- 当品类的时候，需要显示品类1/品类2这种格式 -->
      <span
        v-if="dict.colorType === 'default' || dict.colorType === '' || dict.colorType === undefined"
        :key="dict.value"
        :class="dict.cssClass"
        :index="index"
        :style="styleColor"
      >{{ dict.label || dict.keyPath || dict.name }}
      </span>
      <!-- Tag 样式 -->
      <el-tag
        v-else
        :key="dict.value"
        :class="dict.cssClass"
        :disable-transitions="true"
        :index="index"
        :style="styleColor"
        :type="dict.colorType"
      >
        {{ dict.label || dict.name }}
      </el-tag>
    </template>
  </span>
</template>

<script>

import { getDictDatas2 } from '@/utils/dict'

export default {
  name: 'DictTag',
  props: {
    type: {
      type: String
    },
    value: { type: [Number, String, Boolean, Array] },
    // 字体颜色
    fontColor: { type: String, default: '' }
  },
  computed: {
    // 字体颜色
    styleColor() {
      if (this.fontColor) {
        return 'color:' + this.fontColor
      }
      return ''
    }
  },
  methods: { getDictDatas2 }
}
</script>
<style scoped>
.el-tag + .el-tag {
  margin-left: 10px;
}
</style>
