<template>
  <div id="auth_iframe" v-loading="loading" :style="'height:' + height">
    <iframe
      :src="src"
      frameborder="no"
      style="width: 100%; height: 100%"
      scrolling="auto"
    />
  </div>
</template>
<script>
import { getAccessToken } from '@/utils/auth'
export default {
  props: {
    src: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      height: document.documentElement.clientHeight - 94.5 + 'px;',
      loading: true,
      url: this.src
    }
  },
  mounted: function() {
    // setTimeout(() => {
    //   const iframe = document.querySelector('#auth_iframe')
    //   this.populateIframe(iframe, [['Authorization', 'Bearer ' + getAccessToken()]])
    // }, 0)

    setTimeout(() => {
      this.loading = false
    }, 300)

    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 94.5 + 'px;'
    }
  },
  methods: {
    populateIframe(iframe, headers) {
      const xhr = new XMLHttpRequest()
      xhr.open('GET', this.src)
      xhr.responseType = 'blob'
      headers.forEach((header) => {
        xhr.setRequestHeader(header[0], header[1])
      })
      xhr.onreadystatechange = () => {
        if (xhr.readyState === xhr.DONE) {
          if (xhr.status === 200) {
            iframe.src = URL.createObjectURL(xhr.response)
          }
        }
      }
      xhr.send()
    }
  }
}
</script>
