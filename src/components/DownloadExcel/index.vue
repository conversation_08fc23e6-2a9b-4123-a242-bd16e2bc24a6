<template>
  <el-button
      plain
      size="mini"
      type="primary"
      @click="click"
      :loading="loading"
  >{{ $t('下载') }}
  </el-button>
</template>

<script>
import { exportByGirdOptionColumns, exportByElTableRef } from '@/config/excelExport'
import { utils, writeFile } from 'xlsx-js-style'

export default {
  name: 'DownloadExcel',
  props: {},
  data() {
    return {
      exportByGirdOptionColumns,
      exportByElTableRef,
      loading: false,
      titleTemplate: {
        v: '',// 单元格的值
        t: 's', // 单元格的类型
        s: {
          font: {
            bold: true, // 是否加粗
            name: '宋体',// 字体
            sz: 14 // 字号大小
          },
          //设置标题水平竖直方向居中，并自动换行展示
          alignment: {
            horizontal: 'center',// 水平居中
            vertical: 'center',// 垂直居中
            wrapText: true// 自动换行
          },
          border: {
            top: { style: 'thin', color: { rgb: '000000' } }, // 上边框
            bottom: { style: 'thin', color: { rgb: '000000' } }, // 下边框
            left: { style: 'thin', color: { rgb: '000000' } }, // 左边框
            right: { style: 'thin', color: { rgb: '000000' } } //  右边框
          },
          fill: {
            fgColor: { rgb: 'C0C0C0' } // 单元格背景颜色
          }
        }
      },
      contentTemplate: {
        v: '',
        t: 's',
        s: {
          font: {
            name: '宋体'
          },
          //设置标题水平竖直方向居中，并自动换行展示
          alignment: {
            horizontal: 'left',// 水平居中
            vertical: 'center',// 垂直居中
            wrapText: true// 自动换行
          },
          border: {
            top: { style: 'thin', color: { rgb: '000000' } }, // 上边框
            bottom: { style: 'thin', color: { rgb: '000000' } }, // 下边框
            left: { style: 'thin', color: { rgb: '000000' } }, // 左边框
            right: { style: 'thin', color: { rgb: '000000' } } //  右边框
          }
        }
      }
    }
  },
  mounted() {
  },
  methods: {
    click() {
      this.$emit('click')
      this.loading = true
    },
    downLoadExcel(
        fileName = '下载.xlsx',
        sheetName = 'Sheet1',
        data = [],
        widthArr = []
    ) {
      if (!fileName.endsWith('.xlsx')) {
        fileName = fileName + '.xlsx'
      }
      this.export(data, widthArr, fileName, sheetName)
    },

    export(data, widthArr, filename, sheetName) {
      const items = []
      data.forEach((e) => {
        items.push(Object.values(e).map((item) => {
          return { ...this.contentTemplate, v: item }
        }))
      })
      this.exportFile(Object.keys(data[0]).map((e) => {
        return { ...this.titleTemplate, v: e }
      }), items, widthArr, filename, sheetName)

      this.loading = false
    },
    exportFile(data, dataArr, widthArr, filename, sheetName) {
      let result = [data, ...dataArr]
      let ws = utils.aoa_to_sheet(result)

      // 合并单元格
      // if (!ws['!merges']) ws['!merges'] = []
      // ws['!merges'].push(utils.decode_range('B2:K2'))
      // ws['!merges'].push(utils.decode_range('B3:K3'))
      // ws['!merges'].push(utils.decode_range('B4:K4'))
      // ws['!merges'].push(utils.decode_range('B5:K5'))
      // ws['!merges'].push(utils.decode_range('A2:A5'))
      // ws['!merges'].push(utils.decode_range('L2:L5'))

      // 设置列宽
      // cols 为一个对象数组，依次表示每一列的宽度
      if (!ws['!cols']) ws['!cols'] = []

      if (widthArr && widthArr.length > 0) {
        ws['!cols'] = widthArr.map((e) => {
          return { wpx: e }
        })
      } else {
        ws['!cols'] = data.map((e) => {
          return { wpx: 200 }
        })
      }

      // 设置行高
      // rows 为一个对象数组，依次表示每一行的高度
      if (!ws['!rows']) ws['!rows'] = []
      ws['!rows'] = [
        { hpx: 34.8 },
        ...dataArr.map(() => {
          return { hpx: 14.4 }
        })
      ]
      var wb = utils.book_new()
      utils.book_append_sheet(wb, ws, sheetName)
      writeFile(wb, filename)
    }
  }
}
</script>
<style scoped>
.a {
  color: rgb(190, 190, 191);
}
</style>
