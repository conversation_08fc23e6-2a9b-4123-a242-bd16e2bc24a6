<template>
  <div class="top-right-btn">
    <el-row>
      <el-tooltip
        v-if="onlyCustom"
        class="item"
        effect="dark"
        :content="$t('common.reload')"
        placement="top"
      >
        <el-button size="mini" circle icon="el-icon-refresh" @click="refresh()" />
      </el-tooltip>
      <el-tooltip
        v-if="columns"
        class="item"
        effect="dark"
        :content="$t('rfq.explicitImplicitColumn')"
        placement="top"
      >
        <el-button size="mini" circle icon="el-icon-menu" @click="showColumn()" />
      </el-tooltip>

      <el-tooltip
        v-if="customColumns"
        class="item"
        effect="dark"
        :content="$t('rfq.explicitImplicitColumn')"
        placement="top"
      >
        <el-button
          size="mini"
          circle
          icon="el-icon-menu"

          @click="settingVisible = true"
        />
      </el-tooltip>
    </el-row>
    <el-dialog :title="title" :visible.sync="open" append-to-body>
      <el-transfer
        id="transfer"
        ref="transfer"
        v-model="value"
        :titles="[$t('rfq.display'), $t('rfq.hide')]"
        :data="columns"
        @change="dataChange"
      >
        <span slot-scope="{ option }" :draggable="!option.disabled" @dragstart="drag($event,option)">{{ option.key }} - {{ option.label }}</span>

      </el-transfer>
    </el-dialog>
    <el-dialog
      v-if="settingVisible"
      :before-close="setCustom"
      width="300px"
      append-to-body
      :title="$t('rfq.fieldsDisplayed')"
      :visible.sync="settingVisible"
    >
      <el-scrollbar style="height: 500px;">
        <draggable
          :scroll-sensitivity="100"
          :force-fallback="true"
          :fallback-tolerance="1"
          :set-data="setData"
          :list="customColumns"
          group="article"
          class="setting"
        >
          <div
            v-for="element in customColumns"
            v-show="element.title&&!element?.fixed&&!element.children"
            :key="element.id"
            class="setting-item"
          >
            <div class="list-complete-item-handle">
              <el-checkbox
                v-model="element.visible"
              >
                {{ element.title }}
              </el-checkbox>
            </div>
          </div>
        </draggable>
      </el-scrollbar>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="confirm">{{ $t('common.confirm') }}</el-button>
        <el-button type="primary" @click="restColumn">{{ $t('common.reset') }}</el-button>
      </div>
    </el-dialog>

  </div>
</template>
<script>
import draggable from 'vuedraggable'
import {createFrontTableFieldsUserRel} from "@/api/system/customField";

export default {
  name: 'RightToolbar',
  components: {
    draggable
  },
  props: {
    listId: {
      type: String
    },
    showSearch: {
      type: Boolean,
      default: true
    },
    onlyCustom: {
      type: Boolean,
      default: true
    },
    columns: {
      type: Array
    },
    customColumns: {
      type: Array
    }
  },
  data() {
    return {
      // 显隐数据
      value: [],
      // 弹出层标题
      title: this.$t('rfq.showhide'),
      // 是否显示弹出层
      open: false,
      settingVisible: false,
      storeCustom: ''
    }
  },
  mounted() {
    // 显隐列初始默认隐藏列
    for (const item in this.columns) {
      if (this.columns[item].visible === false) {
        this.value.push(parseInt(item))
      }
    }
    if (this.listId) {
      this.setInitCustom()
      this.initCustomColumn()
    }
  },
  methods: {
    setInitCustom() {
      this.storeCustom = this.customColumns.reduce(
        (a, v, index) => ({ ...a, [v.field]: {
          visible: v.visible,
          sort: index
        }}), {})
      // 记录初始化状态
    },
    initCustomColumn() {
      const temp = localStorage.getItem('customList')
      try {
        const customList = JSON.parse(temp)
        // eslint-disable-next-line no-prototype-builtins
        if (customList?.hasOwnProperty(this.listId)) {
          // eslint-disable-next-line vue/no-mutating-props
          this.customColumns.sort((a, b) => {
            b.visible = customList[this.listId][b.field].visible
            a.visible = customList[this.listId][a.field].visible
            return customList[this.listId][a.field].sort - customList[this.listId][b.field].sort
          })
        }
      } catch (e) {
        console.log(e)
      }
    },
    setCustomStorage() {
      const temp = localStorage.getItem('customList')
      try {
        const customList = JSON.parse(temp) || {}
        customList[this.listId] = this.customColumns.reduce(
          (a, v, index) => ({ ...a, [v.field]: {
            visible: v.visible,
            sort: index
          }}), {})
        localStorage.setItem('customList', JSON.stringify(customList))
        createFrontTableFieldsUserRel({
          userId: this.$store.getters.userId,
          tableId: this.listId,
          fieldsJson: JSON.stringify(customList[this.listId])
        })
      } catch (e) {
        console.log(e)
      }
    },
    setCustom(close) {
      // eslint-disable-next-line vue/no-mutating-props
      this.customColumns.push({})
      // eslint-disable-next-line vue/no-mutating-props
      this.customColumns.pop()
      this.setCustomStorage()
      this.settingVisible = false
      close()
    },
    setData(dataTransfer) {
      dataTransfer.setData('Text', '')
    },
    // 搜索
    toggleSearch() {
      this.$emit('update:showSearch', !this.showSearch)
    },
    // 刷新
    refresh() {
      this.$emit('queryTable')
    },
    // 右侧列表元素变化
    dataChange(data) {
      for (var item in this.columns) {
        const key = this.columns[item].key
        this.columns[item].visible = !data.includes(key)
      }
    },
    // 打开显隐列dialog
    showColumn() {
      this.open = true
    },
    // 确定生效
    confirm() {
      // eslint-disable-next-line vue/no-mutating-props
      this.customColumns.push({})
      // eslint-disable-next-line vue/no-mutating-props
      this.customColumns.pop()
      this.setCustomStorage()
      this.settingVisible = false
    },
    restColumn() {
      const columnWidth = localStorage.getItem('VXE_TABLE_CUSTOM_COLUMN_WIDTH')
      const columnList = localStorage.getItem('customList')
      try {
        const width = JSON.parse(columnWidth) || {}
        width[this.listId] = {}
        localStorage.setItem('VXE_TABLE_CUSTOM_COLUMN_WIDTH', JSON.stringify(width))
        const list = JSON.parse(columnList) || {}
        list[this.listId] = this.storeCustom
        localStorage.setItem('customList', JSON.stringify(list))
        createFrontTableFieldsUserRel({
          userId: this.$store.getters.userId,
          tableId: this.listId,
          fieldsJson: JSON.stringify(this.storeCustom)
        })
      } catch (e) {
        console.log(e)
      }
      this.initCustomColumn()
      this.customColumns.push({})
      this.customColumns.pop()
      this.settingVisible = false
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-transfer__button {
  border-radius: 50%;
  padding: 12px;
  display: block;
  margin-left: 0px;
}
::v-deep .el-transfer__button:first-child {
  margin-bottom: 10px;
}
::v-deep .el-dialog{
  max-height: 1000px!important;
}
.setting{
  margin-left: 30px;
  &-item{
    margin: 10px 0;
  }
}
</style>
