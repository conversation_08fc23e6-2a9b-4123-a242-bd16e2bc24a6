<template>
  <el-cascader
    v-if="reload"
    v-model="value"
    :disabled="disabled"
    :options="categoryList"
    :props="{ value: 'id',label:'name',multiple: multiple}"
    class="content"
    clearable
    filterable
    @change="changeVal"
  />
</template>

<script>
import { DICT_TYPE, getDictDatas } from '@/utils/dict'
import { getTreeMap } from '@/utils'

export default {
  name: 'CascadingCategory',
  props: {
    originalValue: {},
    multiple: {
      type: Boolean,
      default: true
    },
    disabled: {
      type: <PERSON>olean,
      default: false
    },
    // 对品类层级进行限制，传入2，代表只取1和2级
    maxLevel: {
      type: Number,
      default: null
    }
  },
  data() {
    let category = getDictDatas(DICT_TYPE.COMMON_CATEGORY, 0)
    if (this.maxLevel) {
      category = category.filter(item => item.level <= this.maxLevel) || []
    }
    const categoryList = this.handleTree(category, 'id')
    return {
      value: [],
      categoryList,
      reload: true
    }
  },
  watch: {
    originalValue: {
      immediate: true,
      handler(val) {
        if (this.multiple) {
          this.value.length = 0
          val?.map(item => {
            this.value.push(getTreeMap(item, this.categoryList))
          })
          this.reloadCas()
        } else {
          this.value = getTreeMap(val, this.categoryList)
        }
        if (val?.length === 0) {
          // reset时重新渲染组件
          this.value.length = 0
          this.reloadCas()
        }
      }
    },
    maxLevel: {
      immediate: true,
      handler() {
        let category = getDictDatas(DICT_TYPE.COMMON_CATEGORY, 0)
        if (this.maxLevel) {
          category = category.filter(item => item.level <= this.maxLevel) || []
        }
        this.categoryList = this.handleTree(category, 'id')
      }
    }
  },
  methods: {
    changeVal() {
      if (this.multiple) {
        this.$emit('update:originalValue', this.value.map(item => item.at(-1)) || [])
        this.$emit('change', this.value.map(item => item.at(-1)) || [])
      } else {
        this.$emit('update:originalValue', this.value.at(-1) || '')
        this.$emit('change', this.value.at(-1) || '')
      }
    },
    reloadCas() {
      this.reload = false
      this.$nextTick(() => {
        this.reload = true
      })
    }
  }
}
</script>

<style scoped>

</style>
