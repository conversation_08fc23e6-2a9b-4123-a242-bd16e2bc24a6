<template>
  <div>
    <div style="display: flex;justify-content: center;align-items: center;margin-bottom: 25px;">
      <el-input
        v-model="queryParams[searchProp]"
        :placeholder="placeholder"
        style="flex: 0 1 40%"
        clearable
        @keyup.enter.native="search"
      />
      <el-button plain type="primary" @click="search">{{ $t('common.search') }}</el-button>
      <el-button plain style="margin-left: 0" @click="reset">{{ $t('common.reset') }}</el-button>
      <div style="margin-left: 30px;display: flex;align-items: center" @click="showSearch= !showSearch">
        <el-button type="text">{{ $t('common.advancedSearch') }}</el-button>
        <i
          :style="showSearch? '':{transform: 'rotate(180deg)'}"
          class="el-icon-arrow-up"
          style="margin-left:10px;font-size: 18px;cursor:pointer;float: right;transition: transform .3s;
          font-weight: 300;"
        />
      </div>
    </div>
    <div v-show="showSearch" class="commonSearchItem">
      <el-form :inline="true" :model="queryParams" :label-width="labelWidth+'px'" size="small">
        <slot/>
      </el-form>
    </div>

  </div>

</template>

<script>
export default {
  name: 'Search',
  props: {
    queryParams: {
      type: Object
    },
    placeholder: {
      type: String,
      default: '请输入关键字'
    },
    searchProp: {
      type: String,
      default: 'keyword'
    },
    labelWidth: {
      type: Number,
      default: 162
    }
  },
  data() {
    return {
      showSearch: false,
      resetParams: { ...this.queryParams }
    }
  },
  methods: {
    search() {
      this.queryParams.pageNo = 1
      this.$emit('search')
    },
    reset() {
      this.$emit('update:queryParams', { ...this.resetParams })
      this.search()
    }
  }
}
</script>

<style scoped>
.commonSearchItem {
  ::v-deep .el-form-item {
    display: inline-flex !important;
    width: 33.3% !important;
    margin-right: 0;
    margin-bottom: 14px;
  }
}

.commonSearchItem {
  ::v-deep .el-form-item__content {
    flex-grow: 1 !important;
  }
}
.commonSearchItem {
  ::v-deep .el-select {
    width: 100%;
  }
}
.commonSearchItem {
  ::v-deep .el-input {
    width: 100%;
  }
}
.commonSearchItem {
  ::v-deep .el-date-editor {
    width: 100%;
  }
}
.commonSearchItem {
  ::v-deep .el-cascader {
    width: 100%;
  }
}

</style>
