<template>
  <iframe :style="`width:${previewParams.width}px;height:${previewParams.height}px;`" :src="previewParams.url" />
</template>

<script>
import { getPreviewReport } from '@/api/visualization/report'
export default {
  name: 'ReportPreview',
  props: {
    reportId: {
      type: Number,
      required: true
    },
    width: {
      type: Number,
      default: 700
    },
    height: {
      type: Number,
      default: 500
    },
    params: {
      type: Map,
      required: true
    }
  },
  data() {
    return {
      previewParams: {
        height: undefined,
        width: undefined,
        url: ''
      }
    }
  },
  mounted() {
    this.getPreviewUrl()
  },
  methods: {
    getPreviewUrl() {
      /*       if (this.params) {
        for (const [k, v] of this.params) {

        }
      } */
      getPreviewReport({
        reportId: this.reportId,
        ...Object.fromEntries(this.params)
      }).then(res => {
        this.previewParams.url = res.data
        this.previewParams.height = this.height
        this.previewParams.width = this.width
      })
    }
  }
}
</script>

<style scoped>

</style>
