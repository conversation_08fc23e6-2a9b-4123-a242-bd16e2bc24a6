<template>
  <div>
    <!-- 审批信息 -->
    <el-card
      v-show="!viewOnly&&runningTask.showReason"
      v-loading="processInstanceLoading"
      class="commonCard"
    >
      <div slot="header" class="mainTab">
        {{ $t('system.approvalTask') }}【{{ runningTask.name }}】
        <slot name="header" />
        <i
          :style="showProcessInstanceDetail? '':{transform: 'rotate(180deg)'}"
          class="el-icon-arrow-up"
          style="font-size: 18px;cursor:pointer;float: right;transition: transform .3s;
          font-weight: bold;"
          @click="showProcessInstanceDetail= !showProcessInstanceDetail"
        />
      </div>
      <div v-show="showProcessInstanceDetail">
        <el-col :offset="4" :span="16">
          <el-form ref="form" :model="auditForms" :rules="currentRules" :show-require-mark="false" label-width="100px">
            <el-form-item :label="$t('auth.approvalComments')" :required="false" prop="reason">
              <el-input
                v-model="auditForms.reason"
                :placeholder="$t('system.pleaseEnterApprovalComments')"
                :rows="3"
                maxlength="1000"
                show-word-limit
                type="textarea"
              />
            </el-form-item>
          </el-form>
        </el-col>
      </div>
    </el-card>
    <!-- 审批记录 -->
    <common-card
      v-show="showProcessInstance"
      v-loading="processInstanceLoading"
      :default-close="true"
      :title="$t('system.approvalRecord')"
    >
      <el-col :offset="4" :span="16">
        <div class="block">
          <el-timeline class="spec_timeline">
            <el-timeline-item
              v-for="(item, index) in tasks"
              :key="index"
              :icon="getTimelineItemIcon(item)"
              :type="getTimelineItemType(item)"
            >
              <p style="font-weight: 700">{{ item.name }}</p>
              <el-card :body-style="{ padding: '10px' }">
                <label v-if="item.assigneeUser" style="font-weight: normal; margin-right: 30px;">
                  {{ item.assigneeUser.nickname }} </label>
                <label v-if="item.endTime" style="color:#8a909c;font-weight: normal"> {{
                  parseTime(item.endTime)
                }}</label>
                <label
                  v-if="item.durationInMillis"
                  style="margin-left: 30px;font-weight: normal"
                >{{ $t('system.timeConsuming') }}</label>
                <label v-if="item.durationInMillis" style="color:#8a909c;font-weight: normal">
                  {{ getDateStar(item.durationInMillis) }} </label>
                <p v-if="item.reason">
                  <el-tag :type="getTimelineItemType(item)">{{ item.reason }}</el-tag>
                </p>
              </el-card>
            </el-timeline-item>
          </el-timeline>
        </div>
      </el-col>
    </common-card>
    <!-- 流程图 -->
    <common-card
      v-show="showProcessInstance"
      v-loading="processInstanceLoading"
      :default-close="true"
      :is-refresh="true"
      :title="$t('system.flowChart')"
    >
      <my-process-viewer
        key="designer"
        v-model="bpmnXML"
        :activity-data="activityList"
        :process-instance-data="processInstance"
        :task-data="tasks"
        v-bind="bpmnControlForm"
      />
    </common-card>

    <div class="fixedBottom">
      <!--退回到发起人的时候的按钮-->
      <slot name="customButton" />
      <el-button
        v-if="hasRunningTask&&showProcessInstance&&!processInstance.canReject&&!viewOnly&&closeAttribute.visible"
        :loading="btnLoading"
        plain
        type="primary"
        @click="closeAudit(runningTask)"
      >{{
        closeAttribute.value ?? $t('system.closeDocument')
      }}
      </el-button>

      <el-button
        v-if="showProcessInstance&&!processInstance.canReject&&!viewOnly"
        :loading="btnLoading"
        type="primary"
        @click="submitAudit(runningTask)"
      >{{ $t('common.submit') }}
      </el-button>

      <!--非发起人节点的提交按钮-->
      <el-button
        v-if="hasRunningTask&&processInstance.canReject&&!viewOnly&&agreeAttribute.visible"
        :loading="btnLoading"
        plain
        type="primary"
        @click="handleAudit(runningTask)"
      >{{
        agreeAttribute.value ?? $t('system.agree')
      }}
      </el-button>
      <el-button
        v-if="runningTask.showSaveButton&&!viewOnly"
        :loading="btnLoading"
        plain
        type="primary"
        @click="saveAudit"
      >{{ $t('common.save') }}
      </el-button>
      <el-button
        v-if="hasRunningTask&&processInstance.canReject&&!viewOnly && !$store.getters.supplierId"
        plain
        type="warning"
        @click="handleUpdateAssignee(runningTask)"
      >{{
        $t('system.transfer')
      }}
      </el-button>
      <!--UFFF-3091 外部用户需要关闭转办按钮的权限-->

      <el-button
        v-if="hasRunningTask&&runningTask.showRejectButton&&!viewOnly&&rejectAttribute.visible"
        :loading="btnLoading"
        plain
        type="danger"
        @click="handleReject(runningTask)"
      >{{
        rejectAttribute.value ?? $t('rfq.return')
      }}
      </el-button>
    </div>
    <!-- 对话框(转派审批人) -->
    <el-dialog :title="$t('system.transferApprover')" :visible.sync="updateAssignee.open" append-to-body width="500px">
      <el-form ref="updateAssigneeForm" :model="updateAssignee.form" :rules="updateAssignee.rules" label-width="110px">
        <el-form-item :label="$t('system.newApprover')" prop="assigneeUserId">
          <el-select
            v-model="updateAssignee.form.assigneeUserId"
            class="searchValue"
            clearable
            filterable
            style="width: 100%"
          >
            <el-option
              v-for="item in userOptions"
              :key="parseInt(item.id)"
              :label="item.nickname"
              :value="parseInt(item.id)"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelUpdateAssigneeForm">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="submitUpdateAssigneeForm">{{ $t('order.determine') }}</el-button>

      </div>
    </el-dialog>
  </div>
</template>
<script>
import { getBaseHeader } from '@/utils/request'
import { getDate } from '@/utils/dateUtils'
import { getProcessDefinitionBpmnXML } from '@/api/bpm/definition'
import { getActivityList } from '@/api/bpm/activity'
import { getTaskListByProcessInstanceId, updateTaskAssignee } from '@/api/bpm/task'
import store from '@/store'
import { getProcessInstance } from '@/api/bpm/processInstance'
import { parseTime } from '@/utils/ruoyi'
import { listSimpleUsers } from '@/api/system/user'

export default {
  name: 'BpmProcessInstance',
  props: {
    processInstanceId: {
      type: String,
      default: null // 流程实例id
    },
    viewOnly: {
      type: Boolean,
      default: false // 是否查看
    },
    /**
     * 对退回的按钮进行属性配置
     * visible是否显示
     * value按钮名称
     */
    rejectAttribute: {
      type: Object, // 接受一个对象
      default: () => ({ visible: true, value: null }) // 默认值包含 isBoolean 和 value 属性
    },
    /**
     * 对关闭单据的按钮进行属性配置
     * visible是否显示
     * value按钮名称
     */
    closeAttribute: {
      type: Object, // 接受一个对象
      default: () => ({ visible: true, value: null }) // 默认值包含 isBoolean 和 value 属性
    },
    /**
     * 对同意的按钮进行属性配置
     * visible是否显示
     * value按钮名称
     */
    agreeAttribute: {
      type: Object, // 接受一个对象
      default: () => ({ visible: true, value: null }) // 默认值包含 isBoolean 和 value 属性
    }
  },
  emits: ['closeAuditEvent', 'saveAuditEvent', 'submitAuditEvent', 'approvalAuditEvent', 'rejectAuditEvent'],
  data() {
    return {
      parseTime,
      getBaseHeader,
      btnLoading: false,
      showProcessInstanceDetail: true,
      // 是否展示流程相关信息
      showProcessInstance: false,
      // 审批记录
      tasksLoad: true,
      processInstanceLoading: true,
      processInstance: {
        canReject: false
      },
      tasks: [],
      // BPMN 数据
      bpmnXML: null,
      bpmnControlForm: {
        prefix: 'flowable'
      },
      activityList: [],
      // 审批表单
      runningTask: {
        showReason: false, // 是否显示审批意见框
        showSaveButton: false, // 是否显示保存按钮
        showRejectButton: false, // 是否显示拒绝按钮
        name: ''
      },
      // 是否有任务
      hasRunningTask: false,
      auditForms: {
        reason: ''
      },
      auditRule: {
        reason: [{ required: true, message: this.$t('system.approvalCommentsCannotBeEmpty'), trigger: 'blur' }]
      },
      currentRules: {},
      // 转派审批人
      userOptions: [],
      updateAssignee: {
        open: false,
        form: {
          assigneeUserId: undefined
        },
        rules: {
          assigneeUserId: [{ required: true, message: this.$t('system.newApproverCannotBeEmpty'), trigger: 'change' }]
        }
      }
    }
  },
  watch: {
    processInstanceId: {
      handler() {
        this.getDetail()
      },
      immediate: true
    }
  },
  created() {
    // 获得用户列表
    this.userOptions = []
    listSimpleUsers().then(response => {
      this.userOptions.push(...response.data)
    })
  },
  methods: {
    /** 获得流程实例 */
    getDetail() {
      if (this.processInstanceId) {
        this.showProcessInstance = true
        // 获得流程实例相关
        this.processInstanceLoading = true
        getProcessInstance(this.processInstanceId).then(response => {
          if (!response.data) {
            this.$message.error(this.$t('system.unableToFindProcessInformation'))
            return
          }
          // 设置流程信息
          this.processInstance = response.data
          // 加载流程图
          getProcessDefinitionBpmnXML(this.processInstance.processDefinition.id).then(response => {
            this.bpmnXML = response.data
          })
          // 加载活动列表
          getActivityList({
            processInstanceId: this.processInstance.id
          }).then(response => {
            this.activityList = response.data
          })
          // 取消加载中
          this.processInstanceLoading = false
        })

        // 获得流程任务列表（审批记录）
        this.tasksLoad = true
        this.auditForms.reason = ''
        getTaskListByProcessInstanceId(this.processInstanceId).then(response => {
          // 审批记录
          this.tasks = []
          // 移除已取消的审批
          response.data.forEach(task => {
            if (!task.reason?.includes('Change parent activity to')) {
              this.tasks.push(task)
            }
          })
          // 排序，将未完成的排在前面，已完成的排在后面；
          this.tasks.sort((a, b) => {
            // 有已完成的情况，按照完成时间倒序
            return b.createTime - a.createTime
          })

          // 需要审核的记录
          const userId = store.getters.userId
          this.tasks.forEach(task => {
            if (task.result !== 1) { // 只有待处理才需要
              return
            }
            // todo 此处的判断需要加上匿名供应商的判断
            if (task?.anonymous) {
              // todo
            }
            if (!task.assigneeUser || task.assigneeUser.id !== userId) { // 自己不是处理人
              return
            }
            this.runningTask = task
            this.hasRunningTask = true
          })
          this.setRemarkDisabled()
          // 取消加载中
          this.tasksLoad = false
        })
      } else {
        this.showProcessInstance = false
      }
    },
    /** 非审批人不允许修改页面*/
    setRemarkDisabled() {
      if (!this.runningTask) {
        this.$parent.remarkEdit = false
      }
    },
    getDateStar(ms) {
      return getDate(ms)
    },
    getTimelineItemIcon(item) {
      if (item.result === 1) {
        return 'el-icon-time'
      }
      if (item.result === 2) {
        return 'el-icon-check'
      }
      if (item.result === 5) {
        return 'el-icon-close'
      }
      if (item.result === 4) {
        return 'el-icon-remove'
      }
      return ''
    },
    getTimelineItemType(item) {
      if (item.result === 1) {
        return 'primary'
      }
      if (item.result === 2) {
        return 'success'
      }
      if (item.result === 5) {
        return 'danger'
      }
      if (item.result === 4) {
        return 'info'
      }
      return ''
    },
    /** 处理审批通过和不通过的操作 */
    handleAudit(task) {
      this.currentRules = {}
      if ('$listeners' in this && 'approvalAuditEvent' in this.$listeners) {
        this.$emit('approvalAuditEvent', task.id, this.processInstanceId, this.auditForms.reason)
      }
    },
    /** 处理审批拒绝的操作 */
    handleReject(task) {
      // 更新验证规则为原始规则
      this.currentRules = this.auditRule
      // 表单验证
      this.$nextTick(() => {
        this.$refs['form'].validate(valid => {
          if (!valid) {
            this.showProcessInstanceDetail = true
            this.$nextTick(() => {
              this.$refs['form'].$el.scrollIntoView({
                block: 'center',
                behavior: 'smooth'
              })
            })
            return
          }
          if ('$listeners' in this && 'rejectAuditEvent' in this.$listeners) {
            this.$emit('rejectAuditEvent', task.id, this.processInstanceId, this.auditForms.reason)
          }
        })
      })
    },
    /** 关闭单据操作 */
    closeAudit(task) {
      this.$modal.confirm(this.$t('system.theSystemWillCloseTheDocumentAndTheClosedDocumentWillBeArchivedAndSavedDoYouWantToContinue')).then(function() {
        return true
      }).then(() => {
        this.runningTask.showReason = true
        this.currentRules = this.auditRule
        // 表单验证
        this.$nextTick(() => {
          this.$refs['form'].validate(valid => {
            if (!valid) {
              this.showProcessInstanceDetail = true
              this.$nextTick(() => {
                this.$refs['form'].$el.scrollIntoView({
                  block: 'center',
                  behavior: 'smooth'
                })
              })
              this.showProcessInstanceDetail = true
              return
            }

            if ('$listeners' in this && 'closeAuditEvent' in this.$listeners) {
              this.$emit('closeAuditEvent', task.id, this.processInstanceId, this.auditForms.reason)
            }
          })
        })
      }).catch(() => {
      })
    },
    /** 保存的操作 */
    saveAudit() {
      if ('$listeners' in this && 'saveAuditEvent' in this.$listeners) {
        this.$emit('saveAuditEvent')
      }
    },
    /** 提交的操作 */
    submitAudit(task) {
      this.currentRules = {}
      if ('$listeners' in this && 'submitAuditEvent' in this.$listeners) {
        this.$emit('submitAuditEvent', task.id, this.processInstanceId, this.auditForms.reason)
      }
    },
    /** 处理转派审批人 */
    handleUpdateAssignee(task) {
      // 设置表单
      this.resetUpdateAssigneeForm()
      this.updateAssignee.form.id = task.id
      // 设置为打开
      this.updateAssignee.open = true
    },
    /** 重置转派审批人 */
    resetUpdateAssigneeForm() {
      this.updateAssignee.form = {
        id: undefined,
        assigneeUserId: undefined
      }
      this.resetForm('updateAssigneeForm')
    },
    /** 提交转派审批人 */
    submitUpdateAssigneeForm() {
      this.$refs['updateAssigneeForm'].validate(valid => {
        if (!valid) {
          return
        }
        updateTaskAssignee(this.updateAssignee.form).then(response => {
          this.$modal.msgSuccess(this.$t('system.successfullyTransferredTask'))
          this.updateAssignee.open = false
          // 转办后默认没有当前任务
          this.hasRunningTask = false
          this.getDetail() // 获得最新详情
        })
      })
    },
    /** 取消转派审批人 */
    cancelUpdateAssigneeForm() {
      this.updateAssignee.open = false
      this.resetUpdateAssigneeForm()
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/assets/styles/card.scss";

.spec_timeline {
  ::v-deep .el-timeline-item__node--normal {
    background: #ffffff !important;
    width: 18px;
    height: 18px;
  }

  ::v-deep .el-timeline-item__icon {
    font-size: 18px;
  }

  ::v-deep .el-icon-time {
    color: #4d93b9 !important;

  }

  ::v-deep .el-icon-check {
    color: #12cf65 !important;

  }

  ::v-deep .el-icon-close {
    color: #d33a37 !important;

  }

  ::v-deep .el-icon-remove {
    color: #d33a37 !important;

  }
}

.commonCard {
  margin: 10px 0;
}

::v-deep .el-tag {
  white-space: normal;
  height: auto;
  word-break: break-all;
}

</style>
