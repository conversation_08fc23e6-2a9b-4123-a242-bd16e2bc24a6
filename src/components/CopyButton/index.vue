<template>
  <div
    style="display: flex"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
  >
    <el-button
      v-bind="$attrs"
      style="text-decoration: underline;flex:none"
      type="text"
      @click="$emit('click')"
    >
      <slot />

    </el-button>
    <el-tooltip class="item" effect="dark" :content="$t('ppv.copyContent')" placement="right-start">
      <i
        v-show="showCopy"
        v-clipboard:copy="copyMessage"
        v-clipboard:success="()=>{$modal.msgSuccess($t('system.successfullyCopied'))}"
        style="margin-left: 5px;cursor: pointer;flex-shrink: 0"
        class="el-icon-document-copy"
      />    </el-tooltip>

  </div>

</template>
<script>
export default {
  name: 'CopyButton',
  data() {
    return {
      showCopy: false,
      slotContent: ''
    }
  },
  computed: {
    copyMessage() {
      return this.slotContent
    }
  },

  methods: {
    name() {

    },
    handleMouseEnter() {
      this.slotContent = this.$slots.default.at(0)?.text?.trim()
      if (this.copyMessage) {
        this.showCopy = true
      }
    },
    handleMouseLeave() {
      this.showCopy = false
      this.slotContent = ''
    }
  }
}
</script>

<style scoped lang="scss">

</style>
