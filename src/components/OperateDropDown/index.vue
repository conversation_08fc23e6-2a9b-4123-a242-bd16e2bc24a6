
<template>
  <div>
    <el-dropdown v-show="showMenu" @command="handleCommand">
      <span class="el-dropdown-link">
        <i class="el-icon-more" />
      </span>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item v-for="(item,index) in menuItem" v-if="item.show" :command="index">
          {{ item.name }}
        </el-dropdown-item>
        <el-dropdown-item>
          <slot />
        </el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
  </div>
</template>

<script>
export default {
  name: 'OperateDropDown',
  props: {
    menuItem: { type: Array, default: () => [

    ], required: true }
  },
  computed: {
    showMenu() {
      return this.menuItem.some(a => a.show) || this.$slots.default
    }
  },
  methods: {
    handleCommand(cmd) {
      const item = this.menuItem.find((a, index) => index === cmd)
      item.action(item.para)
    }
  }
}
</script>

<style scoped lang="scss">
::v-deep .el-dropdown-link::before{
  content: "";
  position: absolute;
  top: -7px;
  right: -18px;
  bottom: -5px;
  left: -2px
}
</style>
