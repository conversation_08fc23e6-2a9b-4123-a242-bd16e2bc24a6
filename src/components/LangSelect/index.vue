<!--
 * @Author: your name
 * @Date: 2020-09-04 17:32:56
 * @LastEditTime: 2020-09-23 11:10:42
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \ESIC_web\src\components\LangSelect\test.vue
-->
<template>
  <el-dropdown class="international" trigger="click" @command="handleSetLanguage">
    <div>
      <svg-icon class-name="international-icon" icon-class="language" />
    </div>
    <el-dropdown-menu slot="dropdown">
      <el-dropdown-item :disabled="language==='zh'" command="zh">
        中文
      </el-dropdown-item>
      <el-dropdown-item :disabled="language==='en'" command="en">
        English
      </el-dropdown-item>
      <!--      <el-dropdown-item :disabled="language==='es'" command="es">-->
      <!--        Español-->
      <!--      </el-dropdown-item>-->
      <!--      <el-dropdown-item :disabled="language==='ja'" command="ja">-->
      <!--        日本語-->
      <!--      </el-dropdown-item>-->
    </el-dropdown-menu>
  </el-dropdown>
</template>

<script>
export default {
  computed: {
    language() {
      return this.$store.getters.language
    }
  },
  methods: {
    handleSetLanguage(lang) {
      this.$i18n.locale = lang
      this.$store.dispatch('app/setLanguage', lang)
      this.$message({
        message: 'Switch Language Success',
        type: 'success'
      })
      this.refreshView()
    },
    refreshView() {
      // In order to make the cached page re-rendered
      this.$store.dispatch('tagsView/delAllCachedViews', this.$route)
      console.log(this.$route)
      const {
        fullPath
      } = this.$route

      this.$nextTick(() => {
        this.$router.replace({
          path: '/redirect' + fullPath
        })
      })
      location.reload()
    }
  }
}
</script>
