<template>
  <div style="position:relative;">
    <div ref="content" :style="{ maxHeight: expanded ? 'none' : maxHeight + 'px', overflow: 'hidden' }">
      <slot />
    </div>
    <el-button
      v-if="isOverflowing"
      style="    position: absolute;
    right: 6px;
    top: 7px;"
      type="text"
      @click="toggleExpand"
    >{{ expanded ? '收起' : '展开' }}</el-button>
  </div>
</template>

<script>
export default {
  name: 'Expandable',
  props: {
    maxHeight: {
      type: Number,
      required: true
    }
  },
  data() {
    return {
      expanded: false,
      isOverflowing: false
    }
  },
  mounted() {
    this.checkOverflow()
    window.addEventListener('resize', this.checkOverflow)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.checkOverflow)
  },
  methods: {
    toggleExpand() {
      this.expanded = !this.expanded
    },
    checkOverflow() {
      const contentElement = this.$refs.content
      this.isOverflowing = contentElement.scrollHeight > this.maxHeight
    }
  }
}
</script>

<style scoped>
button {
  margin-top: 10px;
}
</style>
