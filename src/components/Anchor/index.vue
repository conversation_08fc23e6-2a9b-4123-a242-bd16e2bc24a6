
<template>
  <div ref="anchor">

    <el-row>
      <div class="navBox">
        <div
          v-for="(item,index) in navList"
          :key="index"
          :class="['navItem', navActive === index+1 ? 'active' : '']"
          @click="goAnchor(item.id, index+1)"
        >
          <span style="font-size: 17px">
            {{ item.label }}
            <slot :name="item.id" />

          </span>
        </div>
      </div>
    </el-row>
    <slot name="body-head" />

    <div style="display: flex;">
      <div
        v-if="$slots['body-left']"
        style="flex: 0 0 200px;"
        :style="`height: calc(100vh - 283px - ${bottomHeight}px);`"
      >
        <slot name="body-left" />
      </div>
      <div style="flex: 0 1 95%;position:relative;overflow: hidden">
        <div id="box" class="box-col" :style="`height: calc(100vh - 283px - ${bottomHeight}px);`">
          <slot name="body" />

        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { debounce, throttle } from 'throttle-debounce'

export default {
  name: 'Anchor',
  props: {
    navList: {
      type: Array,
      default: () => []
    },
    bottomHeight: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      // 锚点点击标识, 不然active就会来回抖动
      navClickFlag: false,
      // 滚动元素id
      scrollWrap: 'box',
      navActive: 1,
      // navList: [
      //   {
      //     label: '通用',
      //     id: 'part1'
      //   },
      //   {
      //     label: '卡片名称2',
      //     id: 'part2'
      //   },
      //   {
      //     label: '卡片名称3',
      //     value: 3,
      //     id: 'part3'
      //   },
      //   {
      //     label: '卡片名称4',
      //     id: 'part4'
      //   }
      //
      // ],
      // 各锚点的高度
      offTopLs: []
    }
  },
  watch: {
    bottomHeight: {
      handler: function() {
        this.getoffTop()
      },
      immediate: true
    }
  },
  mounted() {
    // 监听滚动
    const scrollListener = debounce(30, false, this.handleScroll)
    const resizeListener = debounce(50, false, this.getoffTop)
    window.addEventListener('scroll', scrollListener, true)
    const anchor = this.$refs.anchor
    const resizeObserver = new ResizeObserver(entries => {
      resizeListener()
    })
    resizeObserver.observe(anchor)
    this.$once('hook:beforeDestroy', () => {
      window.removeEventListener('scroll', scrollListener)
      resizeObserver.unobserve(anchor)
    })
    this.$nextTick(() => {
      // 计算锚点的高度
      this.getoffTop()
    })
  },

  methods: {
    // 计算锚点的高度
    getoffTop() {
      const offTop = []
      for (let index = 0; index < this.navList.length; index++) {
        const element = this.navList[index]
        if (document.getElementById(element.id)) {
          offTop.push(document.getElementById(element.id).offsetTop)
        }
      }
      this.offTopLs = offTop
    },
    // 监听滚动
    handleScroll() {
      if (document.getElementById(this.scrollWrap)) {
        const wrap = document.getElementById(this.scrollWrap)
        const scTop = wrap.scrollTop
        for (let index = 0; index < this.offTopLs.length; index++) {
          const element = this.offTopLs[index]
          const offsetTop = wrap.offsetTop
          if (scTop + offsetTop + 1 >= element) {
            this.navActive = index + 1
          }
          if (scTop + wrap.clientHeight + 1 >= wrap.scrollHeight) {
            this.navActive = this.navList.length
          }
        }
      }
    },
    // 锚点导航
    goAnchor(keyId, val) {
      this.navClickFlag = true
      this.navActive = val
      document.getElementById(keyId).scrollIntoView({
        behavior: 'smooth',
        block: 'start',
        inline: 'nearest'
      })
      // setTimeout(() => {
      //   this.navClickFlag = false
      // }, 2000)
    }
  }
}
</script>

<style scoped lang="scss">
html {
  overflow: hidden;
}
.box-col {

  overflow-y: auto;
  box-sizing: border-box;
  scroll-behavior: smooth;
}
.box-card {
  height: 500px;
  margin-bottom: 10px;
}
.navBox {
  margin-left: 10px;
  display: flex;
  border-bottom: 2px solid rgba(0, 0, 0, 0.09);
}
.navItem {
  font-weight: 400;
  color: #000000;
  padding: 10px;
  cursor: pointer;
  border-bottom: 2px solid transparent;
}
.navItem:hover {
  color: #4996b8;
}
.navItem.active {
  color: #4996b8;
  position: relative;
  font-weight: bold;
  &::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 50%;
    transform: translateX(-50%);
    width: 50%;
    height: 2px;
    background-color: #4996b8;
  }
}

</style>
